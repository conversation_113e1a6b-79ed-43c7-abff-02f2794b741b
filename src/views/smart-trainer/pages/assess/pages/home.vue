<template>
    <!-- 考试列表主界面 -->
    <div class="exam-list">
        <!-- 顶部搜索框 -->
        <div class="search-bar">
            <div class="search-input-wrapper">
                <i class="pi pi-search search-icon"></i>
                <input
                    type="text"
                    class="search-input"
                    placeholder="搜索考试名称、类型或标签..."
                    v-model="searchKeyword"
                />
                <i v-if="searchKeyword" class="pi pi-times clear-icon" @click="clearSearch"></i>
            </div>
        </div>

        <!-- 筛选区域 -->
        <div class="filter-section">
            <!-- 部门筛选 -->
            <div class="filter-group">
                <label class="filter-label">部门筛选</label>
                <div class="filter-options">
                    <button
                        class="filter-btn"
                        :class="{ active: !selectedDepartment }"
                        @click="handleDepartmentChange(null)"
                    >
                        全部
                    </button>
                    <button
                        v-for="department in departments"
                        :key="department.id"
                        class="filter-btn"
                        :class="{ active: selectedDepartment?.id === department.id }"
                        @click="handleDepartmentChange(department)"
                    >
                        {{ department.name }}
                    </button>
                </div>
            </div>

            <!-- 考试类型筛选 -->
            <div class="filter-group">
                <label class="filter-label">考试类型</label>
                <div class="filter-options">
                    <button
                        class="filter-btn"
                        :class="{ active: !selectedType }"
                        @click="handleTypeChange(null)"
                    >
                        全部
                    </button>
                    <button
                        v-for="type in examTypes"
                        :key="type.id"
                        class="filter-btn"
                        :class="{ active: selectedType?.id === type.id }"
                        @click="handleTypeChange(type)"
                    >
                        {{ type.name }}
                    </button>
                </div>
            </div>
        </div>

        <!-- 考试列表 -->
        <div class="exam-list-container">
            <!-- 加载状态 -->
            <div v-if="loading" class="loading-container">
                <div class="loading-spinner"></div>
                <p>正在加载考试列表...</p>
            </div>

            <!-- 考试列表内容 -->
            <template v-else>
                <div
                    v-for="exam in filteredExams"
                    :key="exam.id"
                    class="exam-item"
                    @click="handleExamClick(exam)"
                >
                    <!-- 左侧考试图标 -->
                    <div class="exam-icon">
                        <div class="icon-container" :class="exam.type">
                            <i :class="`pi ${exam.icon}`" class="exam-icon-img"></i>
                        </div>
                    </div>

                    <!-- 右侧考试内容 -->
                    <div class="exam-content">
                        <!-- 考试头部信息 -->
                        <div class="exam-header">
                            <h3 class="exam-title">{{ exam.name }}</h3>
                        </div>

                        <!-- 考试描述 -->
                        <p class="exam-description">{{ exam.description }}</p>

                        <!-- 考试详细信息 -->
                        <div class="exam-info">
                            <div class="exam-meta">
                                <span class="meta-item">
                                    <i class="pi pi-clock"></i>
                                    {{ exam.duration }}分钟
                                </span>
                                <span class="meta-item">
                                    <i class="pi pi-file"></i>
                                    {{ exam.questionCount }}题
                                </span>
                                <span class="meta-item">
                                    <i class="pi pi-star"></i>
                                    {{ exam.difficulty }}
                                </span>
                            </div>
                        </div>
                    </div>

                    <!-- 右侧箭头 -->
                    <div class="exam-actions">
                        <i class="pi pi-chevron-right action-arrow"></i>
                    </div>
                </div>

                <!-- 空状态 -->
                <div v-if="filteredExams.length === 0" class="empty-state">
                    <i class="pi pi-inbox empty-icon"></i>
                    <p class="empty-text">暂无符合条件的考试</p>
                    <p class="empty-hint">请尝试调整筛选条件</p>
                </div>
            </template>
        </div>
    </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';

// 路由
const router = useRouter();

// 响应式数据
const selectedDepartment = ref(null);
const selectedType = ref(null);
const loading = ref(false);
const searchKeyword = ref('');

// Mock数据 - 部门列表
const departments = ref([
    { id: 1, name: '之家-通用', code: 'zhijia-general' },
    { id: 2, name: '之家-网点', code: 'zhijia-branch' },
    { id: 3, name: '之家-大客', code: 'zhijia-enterprise' }
]);

// Mock数据 - 考试类型
const examTypes = ref([
    { id: 1, name: '廉洁合规' },
    { id: 2, name: '业务技能' },
    { id: 3, name: '安全培训' },
    { id: 4, name: '产品知识' }
]);

// Mock数据 - 考试列表
const exams = ref([
    {
        id: 'exam_001',
        name: '廉洁合规测试',
        description: '本测试包含单选题、多选题和判断题，请仔细阅读题目后作答',
        type: 'compliance',
        typeLabel: '廉洁合规',
        departmentId: 1,
        typeId: 1,
        duration: 30,
        questionCount: 15,
        difficulty: '中等',
        status: 'available',
        icon: 'pi-shield',
        tags: ['廉洁', '合规', '必修']
    },
    {
        id: 'exam_002',
        name: '汽车销售技能考核',
        description: '测试汽车销售相关的专业知识和技能掌握情况',
        type: 'skill',
        typeLabel: '业务技能',
        departmentId: 2,
        typeId: 2,
        duration: 45,
        questionCount: 20,
        difficulty: '困难',
        status: 'available',
        icon: 'pi-car',
        tags: ['销售', '技能', '专业']
    },
    {
        id: 'exam_003',
        name: '信息安全培训考试',
        description: '涵盖网络安全、数据保护、密码管理等安全知识',
        type: 'security',
        typeLabel: '安全培训',
        departmentId: 1,
        typeId: 3,
        duration: 25,
        questionCount: 12,
        difficulty: '简单',
        status: 'available',
        icon: 'pi-lock',
        tags: ['安全', '培训', '必修']
    },
    {
        id: 'exam_004',
        name: '新车型产品知识测试',
        description: '最新上市车型的产品特点、配置参数等知识考核',
        type: 'product',
        typeLabel: '产品知识',
        departmentId: 2,
        typeId: 4,
        duration: 35,
        questionCount: 18,
        difficulty: '中等',
        status: 'available',
        icon: 'pi-cog',
        tags: ['产品', '新车', '知识']
    },
    {
        id: 'exam_005',
        name: '客户服务标准考试',
        description: '客户接待、服务流程、投诉处理等服务标准考核',
        type: 'service',
        typeLabel: '业务技能',
        departmentId: 3,
        typeId: 2,
        duration: 40,
        questionCount: 16,
        difficulty: '中等',
        status: 'completed',
        icon: 'pi-users',
        tags: ['服务', '客户', '标准']
    }
]);

// 计算属性 - 根据筛选条件过滤考试
const filteredExams = computed(() => {
    let result = exams.value;

    // 按部门筛选
    if (selectedDepartment.value) {
        result = result.filter(exam => exam.departmentId === selectedDepartment.value.id);
    }

    // 按类型筛选
    if (selectedType.value) {
        result = result.filter(exam => exam.typeId === selectedType.value.id);
    }

    // 按关键词搜索
    if (searchKeyword.value) {
        result = result.filter(
            exam =>
                exam.name.toLowerCase().includes(searchKeyword.value.toLowerCase()) ||
                exam.description.toLowerCase().includes(searchKeyword.value.toLowerCase()) ||
                exam.typeLabel.toLowerCase().includes(searchKeyword.value.toLowerCase())
        );
    }

    return result;
});

onMounted(() => {
    console.log('考试列表页已加载');
});

// 方法
const clearSearch = () => {
    searchKeyword.value = '';
};

const handleDepartmentChange = department => {
    loading.value = true;
    selectedDepartment.value = department;

    // 模拟加载延迟
    setTimeout(() => {
        loading.value = false;
    }, 300);
};

const handleTypeChange = type => {
    loading.value = true;
    selectedType.value = type;

    // 模拟加载延迟
    setTimeout(() => {
        loading.value = false;
    }, 300);
};

const handleExamClick = exam => {
    console.log('点击考试:', exam);
    // 跳转到考试详情页
    router.push(`/smart-trainer/assess/detail/${exam.id}`);
};
</script>

<style lang="scss" scoped>
.exam-list {
    height: 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

// 搜索栏样式
.search-bar {
    padding: 16px;
    .search-input-wrapper {
        position: relative;
        display: flex;
        align-items: center;

        .search-icon {
            position: absolute;
            left: 12px;
            color: #6c757d;
            font-size: 16px;
        }

        .search-input {
            width: 100%;
            height: 44px;
            padding: 0 40px 0 40px;
            border: 2px solid #e9ecef;
            border-radius: 22px;
            font-size: 14px;
            background: #f8f9fa;
            transition: all 0.3s ease;

            &:focus {
                outline: none;
                border-color: #4299e1;
                background: white;
            }

            &::placeholder {
                color: #adb5bd;
            }
        }

        .clear-icon {
            position: absolute;
            right: 12px;
            color: #6c757d;
            font-size: 16px;
            cursor: pointer;
            transition: color 0.2s ease;

            &:hover {
                color: #495057;
            }
        }
    }
}
// 筛选区域样式
.filter-section {
    padding: 0 16px 16px;

    .filter-group {
        margin-bottom: 16px;

        &:last-child {
            margin-bottom: 0;
        }

        .filter-label {
            display: block;
            font-size: 14px;
            font-weight: 600;
            color: #495057;
            margin-bottom: 8px;
        }

        .filter-options {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;

            .filter-btn {
                padding: 6px 16px;
                border: 1px solid #dee2e6;
                border-radius: 20px;
                background: white;
                color: #6c757d;
                font-size: 13px;
                cursor: pointer;
                transition: all 0.2s ease;

                &:hover {
                    border-color: #4299e1;
                    color: #4299e1;
                }

                &.active {
                    background: #4299e1;
                    border-color: #4299e1;
                    color: white;
                }
            }
        }
    }
}
// 考试列表样式
.exam-list-container {
    flex: 1;
    overflow-y: auto;
    padding: 16px;

    .loading-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 200px;
        color: #6c757d;

        .loading-spinner {
            width: 32px;
            height: 32px;
            border: 3px solid #e9ecef;
            border-top: 3px solid #4299e1;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 16px;
        }

        @keyframes spin {
            0% {
                transform: rotate(0deg);
            }
            100% {
                transform: rotate(360deg);
            }
        }
    }

    .exam-item {
        background: white;
        border-radius: 16px;
        padding: 20px;
        margin-bottom: 16px;
        cursor: pointer;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
        border: 1px solid rgba(0, 0, 0, 0.04);
        display: flex;
        align-items: center;
        gap: 16px;

        &:hover {
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.08);
            border-color: rgba(22, 119, 255, 0.1);
        }

        &:last-child {
            margin-bottom: 0;
        }

        .exam-icon {
            flex-shrink: 0;

            .icon-container {
                width: 60px;
                height: 60px;
                border-radius: 12px;
                display: flex;
                align-items: center;
                justify-content: center;

                &.compliance {
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                }

                &.skill {
                    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
                }

                &.security {
                    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
                }

                &.product {
                    background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
                }

                &.service {
                    background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
                }

                .exam-icon-img {
                    font-size: 24px;
                    color: white;
                }
            }
        }

        .exam-content {
            flex: 1;
            min-width: 0;
        }

        .exam-header {
            margin-bottom: 8px;

            .exam-title {
                font-size: 16px;
                font-weight: 600;
                color: #262626;
                margin: 0;
                line-height: 1.5;
                display: -webkit-box;
                -webkit-line-clamp: 2;
                line-clamp: 2;
                -webkit-box-orient: vertical;
                overflow: hidden;
            }
        }

        .exam-description {
            font-size: 14px;
            color: #595959;
            margin-bottom: 12px;
            line-height: 1.5;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .exam-info {
            .exam-meta {
                display: flex;
                gap: 20px;
                flex-wrap: wrap;

                .meta-item {
                    display: flex;
                    align-items: center;
                    gap: 6px;
                    font-size: 14px;
                    color: #595959;

                    i {
                        font-size: 14px;
                        color: #8c8c8c;
                        flex-shrink: 0;
                    }
                }
            }
        }

        .exam-actions {
            flex-shrink: 0;
            display: flex;
            align-items: center;
            margin-left: 8px;

            .action-arrow {
                font-size: 16px;
                color: #d9d9d9;
                transition: color 0.2s ease;
            }
        }

        &:hover .exam-actions .action-arrow {
            color: #1677ff;
        }
    }

    .empty-state {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 200px;
        color: #6c757d;

        .empty-icon {
            font-size: 48px;
            margin-bottom: 16px;
            opacity: 0.5;
        }

        .empty-text {
            font-size: 16px;
            font-weight: 500;
            margin-bottom: 4px;
        }

        .empty-hint {
            font-size: 14px;
            opacity: 0.7;
        }
    }
}

// 响应式设计
@media (max-width: 768px) {
    .exam-list-container {
        padding: 12px;

        .exam-item {
            padding: 16px;
            gap: 12px;

            .exam-icon .icon-container {
                width: 48px;
                height: 48px;

                .exam-icon-img {
                    font-size: 20px;
                }
            }

            .exam-content {
                .exam-header {
                    margin-bottom: 6px;

                    .exam-title {
                        font-size: 15px;
                        line-height: 1.4;
                    }
                }

                .exam-description {
                    font-size: 13px;
                    margin-bottom: 10px;
                }
            }

            .exam-info .exam-meta {
                gap: 16px;

                .meta-item {
                    font-size: 13px;

                    i {
                        font-size: 13px;
                    }
                }
            }

            .exam-actions .action-arrow {
                font-size: 14px;
            }
        }
    }

    .filter-section {
        padding: 0 12px 12px;

        .filter-group .filter-options .filter-btn {
            padding: 5px 12px;
            font-size: 12px;
        }
    }

    .search-bar {
        padding: 12px;

        .search-input-wrapper .search-input {
            height: 40px;
            font-size: 13px;
        }
    }
}

@media (max-width: 480px) {
    .exam-list-container {
        padding: 10px;

        .exam-item {
            padding: 12px;
            gap: 10px;
            border-radius: 12px;

            .exam-icon .icon-container {
                width: 40px;
                height: 40px;
                border-radius: 10px;

                .exam-icon-img {
                    font-size: 18px;
                }
            }

            .exam-content {
                .exam-header {
                    margin-bottom: 6px;

                    .exam-title {
                        font-size: 14px;
                        line-height: 1.3;
                    }
                }

                .exam-description {
                    font-size: 12px;
                    margin-bottom: 8px;
                    line-height: 1.4;
                }
            }

            .exam-info .exam-meta {
                gap: 12px;
                flex-wrap: wrap;

                .meta-item {
                    font-size: 12px;
                    gap: 4px;

                    i {
                        font-size: 12px;
                    }
                }
            }

            .exam-actions {
                margin-left: 4px;

                .action-arrow {
                    font-size: 14px;
                }
            }
        }
    }

    .filter-section .filter-group .filter-options {
        gap: 6px;

        .filter-btn {
            padding: 4px 10px;
            font-size: 12px;
        }
    }
}
</style>
