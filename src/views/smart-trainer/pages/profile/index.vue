<template>
    <div class="profile-page">
        <!-- 用户信息区 -->
        <div class="user-info">
            <div class="user-avatar">
                <img :src="userInfo.avatar" :alt="userInfo.name" />
            </div>
            <div class="user-details">
                <h2 class="user-name">{{ userInfo.name }}</h2>
            </div>
        </div>

        <!-- 核心数据统计 -->
        <div class="stats-section">
            <div v-for="stat in statsData" :key="stat.key" class="stat-item">
                <div class="stat-number">{{ stat.value }}</div>
                <div class="stat-label">{{ stat.label }}</div>
            </div>
        </div>

        <!-- 学习数据卡片 -->
        <div class="data-cards">
            <!-- 排行榜卡片 -->
            <div class="data-card ranking-card">
                <div class="card-header">
                    <span class="card-title">学习排行榜</span>
                    <i class="card-arrow">></i>
                </div>
                <div class="card-content">
                    <div class="content-label">本周排名</div>
                    <div class="content-value ranking-value">{{ rankingData.weeklyRank }}+</div>
                </div>
            </div>

            <!-- 学习报告卡片 -->
            <div class="data-card report-card">
                <div class="card-header">
                    <span class="card-title">学习报告</span>
                    <i class="card-arrow">></i>
                </div>
                <div class="card-content">
                    <div class="study-item">
                        <div class="content-label">本周学习</div>
                        <div class="content-value">
                            {{ studyData.weeklyHours }} 时 {{ studyData.weeklyMinutes }} 分
                        </div>
                    </div>
                    <div class="study-item">
                        <div class="content-label">今日学习</div>
                        <div class="content-value">
                            {{ studyData.todayHours }} 时 {{ studyData.todayMinutes }} 分
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 列表功能菜单 -->
        <div class="menu-section">
            <div
                v-for="menuItem in menuItems"
                :key="menuItem.key"
                class="menu-item"
                @click="handleMenuClick(menuItem.key)"
            >
                <div class="menu-icon">
                    <i :class="menuItem.iconClass"></i>
                </div>
                <div class="menu-label">{{ menuItem.label }}</div>
                <div class="menu-arrow">
                    <i class="arrow-icon">></i>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';

// 添加路由功能
const router = useRouter();

// 响应式状态
const isLoading = ref(false);

// 用户信息
const userInfo = ref({
    name: '马飞飞',
    avatar: 'https://z.autoimg.cn/dealer_microfe_aidev/sales-ai-trainer/portal/swiper.png' // 默认头像
});

// 核心统计数据
const statsData = ref([
    { key: 'study', label: '学习', value: 0 },
    { key: 'practice', label: '练习', value: 0 },
    { key: 'exam', label: '考试', value: 0 },
    { key: 'evaluation', label: '评价', value: 0 }
]);

// 排行榜数据
const rankingData = ref({
    weeklyRank: 500
});

// 学习数据
const studyData = ref({
    weeklyHours: 0,
    weeklyMinutes: 0,
    todayHours: 0,
    todayMinutes: 0
});

// 菜单项 - 添加学习排行榜
const menuItems = ref([
    {
        key: 'ranking',
        label: '学习排行榜',
        iconClass: 'icon-trophy'
    },
    {
        key: 'practice-record',
        label: '练习记录',
        iconClass: 'icon-clock'
    }
]);

/**
 * 处理菜单点击事件
 * @param {string} menuKey - 菜单项的key
 */
const handleMenuClick = menuKey => {
    console.log('点击菜单项:', menuKey);
    // 根据不同的菜单项执行不同的操作
    switch (menuKey) {
        case 'ranking':
            // 跳转到学习排行榜页面
            router.push('/smart-trainer/practice/rank');
            break;
        case 'practice-record':
            // 跳转到练习记录页面
            router.push('/smart-trainer/practice/my');
            break;
        default:
            break;
    }
};

/**
 * 获取用户数据
 */
const fetchUserData = () => {
    isLoading.value = true;
    try {
        // 模拟API调用获取用户数据
        // const response = await userService.getUserProfile();
        // userInfo.value = response.userInfo;
        // statsData.value = response.stats;
        console.log('获取用户数据');
    } catch (error) {
        console.error('获取用户数据失败:', error);
    } finally {
        isLoading.value = false;
    }
};

/**
 * 初始化页面
 */
const init = () => {
    console.log('个人中心页面初始化');
    fetchUserData();
};

onMounted(() => {
    init();
});
</script>

<style lang="scss" scoped>
.profile-page {
    height: 100%;
    width: 100%;
    background: linear-gradient(180deg, #f8fcff 0%, #f0f8ff 20%, #f5f5f5 40%, #ffffff 100%);
    min-height: 100vh;

    // 用户信息区
    .user-info {
        display: flex;
        align-items: center;
        padding: 20px 16px;

        .user-avatar {
            width: 60px;
            height: 60px;
            margin-right: 16px;

            img {
                width: 100%;
                height: 100%;
                border-radius: 50%;
                background-color: #b0e0e6; // 淡青色背景
                object-fit: cover;
            }
        }

        .user-details {
            flex: 1;

            .user-name {
                font-size: 18px;
                font-weight: 600;
                color: #333;
            }
        }
    }

    // 核心数据统计
    .stats-section {
        display: flex;

        .stat-item {
            flex: 1;
            text-align: center;

            .stat-number {
                font-size: 20px;
                font-weight: 600;
                color: #333;
                margin-bottom: 4px;
                line-height: 1;
            }

            .stat-label {
                font-size: 14px;
                color: #666;
                font-weight: 600;
            }
        }
    }

    // 学习数据卡片
    .data-cards {
        display: flex;
        gap: 8px;
        padding: 0 16px;
        margin-bottom: 12px;

        .data-card {
            flex: 1;
            background-color: #fff;
            border-radius: 12px;
            padding: 16px;
            margin-top: 20px;

            .card-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 16px;

                .card-title {
                    font-size: 16px;
                    font-weight: 600;
                    color: #333;
                }

                .card-arrow {
                    font-size: 14px;
                    color: #999;
                }
            }

            .card-content {
                .content-label {
                    font-size: 12px;
                    color: #666;
                    margin-bottom: 8px;
                }

                .content-value {
                    font-size: 18px;
                    font-weight: 600;
                    color: #333;
                }

                &.ranking-value {
                    font-size: 24px;
                }
            }
        }

        .report-card {
            .study-item {
                &:not(:last-child) {
                    margin-bottom: 16px;
                }
            }
        }
    }

    // 列表功能菜单
    .menu-section {
        background-color: #fff;
        border-radius: 12px;
        margin: 0 16px;
        overflow: hidden;

        .menu-item {
            display: flex;
            align-items: center;
            padding: 16px;
            border-bottom: 1px solid #f5f5f5;
            cursor: pointer;
            transition: background-color 0.2s ease;

            &:last-child {
                border-bottom: none;
            }

            &:active {
                background-color: #f8f8f8;
            }

            .menu-icon {
                width: 24px;
                height: 24px;
                margin-right: 12px;
                display: flex;
                align-items: center;
                justify-content: center;

                .icon-trophy {
                    width: 20px;
                    height: 20px;
                    background-color: #ff9500;
                    border-radius: 50%;
                    position: relative;

                    &::before {
                        content: '🏆';
                        position: absolute;
                        top: 50%;
                        left: 50%;
                        transform: translate(-50%, -50%);
                        font-size: 12px;
                    }
                }

                .icon-clock {
                    width: 20px;
                    height: 20px;
                    background-color: #4caf50;
                    border-radius: 50%;
                    position: relative;

                    &::before {
                        content: '🕐';
                        position: absolute;
                        top: 50%;
                        left: 50%;
                        transform: translate(-50%, -50%);
                        font-size: 12px;
                    }
                }
            }

            .menu-label {
                flex: 1;
                font-size: 16px;
                color: #333;
            }

            .menu-arrow {
                .arrow-icon {
                    font-size: 14px;
                    color: #999;
                }
            }
        }
    }
}
</style>
