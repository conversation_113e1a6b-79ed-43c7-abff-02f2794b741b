<template>
    <!-- iframe 查看器 -->
    <IframeViewer
        v-if="showIframe"
        :url="currentCourseUrl"
        :title="currentCourseTitle"
        @back="handleIframeBack"
        @load="handleIframeLoad"
        @error="handleIframeError"
    />

    <!-- 课程筛选主界面 -->
    <div v-else class="course-filter">
        <!-- 顶部筛选框 -->
        <div class="search-bar">
            <div class="search-input-wrapper">
                <i class="pi pi-search search-icon"></i>
                <input
                    type="text"
                    class="search-input"
                    placeholder="搜索课程名称、来源或标签..."
                    v-model="searchKeyword"
                />
                <i v-if="searchKeyword" class="pi pi-times clear-icon" @click="clearSearch"></i>
            </div>
        </div>

        <!-- 筛选框容器 -->
        <div class="filter-container">
            <!-- 左侧部门筛选 -->
            <div class="department-filter">
                <div class="department-list">
                    <div
                        v-for="dept in departments"
                        :key="dept.id"
                        class="department-item"
                        :class="{ active: selectedDepartment?.id === dept.id }"
                        @click="handleDepartmentChange(dept)"
                    >
                        {{ dept.name }}
                    </div>
                </div>
            </div>

            <!-- 右侧内容区域 -->
            <div class="content-area">
                <!-- 课程标签筛选 -->
                <div class="tag-filter">
                    <div class="tag-list">
                        <div
                            class="tag-item"
                            :class="{ active: !selectedTag }"
                            @click="handleTagChange(null)"
                        >
                            全部
                        </div>
                        <div
                            v-for="tag in filteredTags"
                            :key="tag.id"
                            class="tag-item"
                            :class="{ active: selectedTag?.id === tag.id }"
                            @click="handleTagChange(tag)"
                        >
                            {{ tag.name }}
                        </div>
                    </div>
                </div>

                <!-- 课程列表 -->
                <div class="course-list">
                    <!-- 课程列表内容 -->
                    <template v-if="!loading">
                        <div
                            v-for="course in filteredCourses"
                            :key="course.id"
                            class="course-item"
                            @click="handleCourseClick(course)"
                        >
                            <!-- 左侧课程图片 -->
                            <div class="course-image">
                                <div class="image-container" :class="course.type">
                                    <img
                                        :src="course.imageUrl"
                                        :alt="course.name"
                                        class="course-image-img"
                                        @error="handleImageError"
                                        @load="handleImageLoad"
                                    />
                                    <!-- 图片加载失败时的后备图标 -->
                                    <i
                                        v-if="course.imageLoadError"
                                        :class="`pi ${course.icon}`"
                                        class="course-icon fallback-icon"
                                    ></i>
                                </div>
                            </div>

                            <!-- 右侧课程信息 -->
                            <div class="course-content">
                                <div class="course-header">
                                    <h3 class="course-title">{{ course.name }}</h3>
                                    <div class="course-type-badge" :class="course.type">
                                        <img
                                            :src="getTypeBadgeImage(course.type)"
                                            :alt="course.typeLabel"
                                            class="badge-image"
                                        />
                                    </div>
                                </div>
                                <div class="course-info">
                                    <div class="info-row">
                                        <div class="info-item">
                                            <i class="pi pi-building info-icon"></i>
                                            <span class="value">{{ course.source }}</span>
                                        </div>
                                        <div class="info-item">
                                            <i class="pi pi-book info-icon"></i>
                                            <span class="value">{{ course.format }}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 空状态 -->
                        <div v-if="filteredCourses.length === 0" class="empty-state">
                            <i class="pi pi-book"></i>
                            <p>暂无相关课程</p>
                        </div>
                    </template>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import IframeViewer from '@/views/smart-trainer/pages/learn/components/IframeViewer.vue';

// 响应式数据
const selectedDepartment = ref(null);
const selectedTag = ref(null);
const loading = ref(false);
const searchKeyword = ref('');

// iframe 相关状态
const showIframe = ref(false);
const currentCourseUrl = ref('');
const currentCourseTitle = ref('');

// Mock数据 - 部门列表
const departments = ref([
    { id: 1, name: '之家-通用', code: 'zhijia-general' },
    { id: 2, name: '之家-网点', code: 'zhijia-branch' },
    { id: 3, name: '之家-大客', code: 'zhijia-enterprise' }
]);

// Mock数据 - 课程标签
const courseTags = ref([
    { id: 1, name: '人力课程', departmentIds: [1] },
    { id: 2, name: '内审课程', departmentIds: [1] },
    { id: 3, name: '法务课程', departmentIds: [1] },
    { id: 4, name: '产品介绍', departmentIds: [2] },
    { id: 5, name: '产品应用', departmentIds: [2] }
]);

// Mock数据 - 课程列表
const courses = ref([
    {
        id: 1,
        name: '【2024版】汽车之家员工手册.pdf',
        source: '人力资源部',
        type: 'document',
        typeLabel: '课件',
        format: '课件（32页）',
        departmentId: 1,
        tagIds: [1],
        imageUrl: 'https://z.autoimg.cn/dealer_microfe_aidev/assets/chat-knowledge-h5/smart-trainer/learn/coverurl.jpg', // 实际图片URL，由用户提供
        icon: 'pi-file-pdf',
        imageLoadError: false,
        targetUrl:
            'https://zhishi.autohome.com.cn/home/<USER>/file?targetId=9991127442514966478848'
    },
    {
        id: 2,
        name: '内审稽核系列.mp4',
        source: '内审部',
        type: 'video',
        typeLabel: '视频',
        format: '视频（10分钟）',
        departmentId: 1,
        tagIds: [2],
        imageUrl: 'https://z.autoimg.cn/dealer_microfe_aidev/assets/chat-knowledge-h5/smart-trainer/learn/coverurl.jpg', // 实际图片URL，由用户提供
        icon: 'pi-play-circle',
        imageLoadError: false,
        targetUrl: 'https://example.com/course/internal-audit-series.mp4'
    },
    {
        id: 3,
        name: '合规普法培训.mp4',
        source: '法务部',
        type: 'document',
        typeLabel: '课件',
        format: '课件（128页）',
        departmentId: 1,
        tagIds: [3],
        imageUrl: 'https://z.autoimg.cn/dealer_microfe_aidev/assets/chat-knowledge-h5/smart-trainer/learn/coverurl.jpg', // 实际图片URL，由用户提供
        icon: 'pi-shield',
        imageLoadError: false,
        targetUrl: 'https://example.com/course/compliance-legal-training.pdf'
    },
    {
        id: 4,
        name: '售卖-2025Q3智慧助手招商方案.pdf ',
        source: '网点',
        type: 'document',
        typeLabel: '课件',
        format: '课件（10页）',
        departmentId: 2,
        tagIds: [4],
        imageUrl: 'https://z.autoimg.cn/dealer_microfe_aidev/assets/chat-knowledge-h5/smart-trainer/learn/coverurl.jpg', // 实际图片URL，由用户提供
        icon: 'pi-briefcase',
        imageLoadError: false,
        targetUrl: 'https://example.com/course/smart-assistant-investment-plan-2025q3.pdf'
    },
    {
        id: 5,
        name: '售卖-2025智效店铺产品介绍.pdf ',
        source: '网点',
        type: 'document',
        typeLabel: '课件',
        format: '课件（10页）',
        departmentId: 2,
        tagIds: [4],
        imageUrl: 'https://z.autoimg.cn/dealer_microfe_aidev/assets/chat-knowledge-h5/smart-trainer/learn/coverurl.jpg', // 实际图片URL，由用户提供
        icon: 'pi-shopping-bag',
        imageLoadError: false,
        targetUrl: 'https://example.com/course/smart-store-product-intro-2025.pdf'
    },
    {
        id: 6,
        name: '【2025司南AI引擎版】产品权益及应用.pdf ',
        source: '网点',
        type: 'document',
        typeLabel: '课件',
        format: '课件（24页）',
        departmentId: 2,
        tagIds: [5],
        imageUrl: '', // 实际图片URL，由用户提供
        icon: 'pi-sparkles',
        imageLoadError: false,
        targetUrl: 'https://example.com/course/sinan-ai-engine-2025-benefits.pdf'
    }
]);

// 计算属性 - 根据选中部门筛选标签
const filteredTags = computed(() => {
    if (!selectedDepartment.value) {
        return courseTags.value;
    }
    return courseTags.value.filter(tag => tag.departmentIds.includes(selectedDepartment.value.id));
});

// 计算属性 - 根据筛选条件过滤课程
const filteredCourses = computed(() => {
    let result = courses.value;

    // 按部门筛选
    if (selectedDepartment.value) {
        result = result.filter(course => course.departmentId === selectedDepartment.value.id);
    }

    // 按标签筛选
    if (selectedTag.value) {
        result = result.filter(course => course.tagIds.includes(selectedTag.value.id));
    }

    // 按关键词搜索
    if (searchKeyword.value) {
        result = result.filter(
            course =>
                course.name.toLowerCase().includes(searchKeyword.value.toLowerCase()) ||
                course.source.toLowerCase().includes(searchKeyword.value.toLowerCase()) ||
                course.format.toLowerCase().includes(searchKeyword.value.toLowerCase())
        );
    }

    return result;
});

// 类型图片映射
const typeImageMap = {
    document:
        'https://z.autoimg.cn/dealer_microfe_aidev/assets/chat-knowledge-h5/smart-trainer/learn/ppt.png', // 课件类型图片占位
    video: 'https://z.autoimg.cn/dealer_microfe_aidev/assets/chat-knowledge-h5/smart-trainer/learn/video.png' // 视频类型图片占位
};

/**
 * 根据课程类型获取对应的徽章图片
 * @param {string} type - 课程类型
 * @returns {string} 图片URL
 */
const getTypeBadgeImage = type => {
    return typeImageMap[type] || '/images/badge-default.png';
};

// 事件处理
const handleDepartmentChange = dept => {
    loading.value = true;
    selectedDepartment.value = dept;
    // 切换部门时重置标签选择
    selectedTag.value = null;

    // 模拟加载延迟
    setTimeout(() => {
        loading.value = false;
    }, 500);
};

const handleTagChange = tag => {
    loading.value = true;
    selectedTag.value = tag;

    // 模拟加载延迟
    setTimeout(() => {
        loading.value = false;
    }, 300);
};

/**
 * 处理课程点击事件，使用 iframe 打开课程链接
 * @param {Object} course - 课程对象
 */
const handleCourseClick = course => {
    console.log('点击课程:', course);

    // 验证目标URL是否存在
    if (!course.targetUrl) {
        console.warn('课程缺少目标链接:', course.name);
        return;
    }

    try {
        // 验证URL格式
        new URL(course.targetUrl);

        // 设置 iframe 相关数据并显示
        currentCourseUrl.value = course.targetUrl;
        currentCourseTitle.value = course.name;
        showIframe.value = true;
    } catch (error) {
        console.error('无效的课程链接:', course.targetUrl, error);
        // 可以在这里添加用户提示，比如 Toast 消息
    }
};

/**
 * 处理 iframe 返回事件
 */
const handleIframeBack = () => {
    showIframe.value = false;
    currentCourseUrl.value = '';
    currentCourseTitle.value = '';
};

/**
 * 处理 iframe 加载完成事件
 */
const handleIframeLoad = () => {
    console.log('iframe 加载完成');
};

/**
 * 处理 iframe 加载错误事件
 */
const handleIframeError = error => {
    console.error('iframe 加载错误:', error);
    // 可以在这里添加错误处理逻辑，比如显示错误提示
};

/**
 * 处理图片加载错误事件
 * @param {Event} event - 图片错误事件
 */
const handleImageError = event => {
    const img = event.target;
    const courseId = img.closest('.course-item')?.querySelector('.course-title')?.textContent;

    // 找到对应的课程并标记图片加载失败
    const course = courses.value.find(c => c.name === courseId);
    if (course) {
        course.imageLoadError = true;
    }

    console.warn('课程图片加载失败:', img.src);
};

/**
 * 处理图片加载成功事件
 * @param {Event} event - 图片加载事件
 */
const handleImageLoad = event => {
    const img = event.target;
    const courseId = img.closest('.course-item')?.querySelector('.course-title')?.textContent;

    // 找到对应的课程并重置错误状态
    const course = courses.value.find(c => c.name === courseId);
    if (course) {
        course.imageLoadError = false;
    }
};

const clearSearch = () => {
    searchKeyword.value = '';
};

// 初始化
onMounted(() => {
    // 默认选择第一个部门
    selectedDepartment.value = departments.value[0];
});
</script>

<style lang="scss" scoped>
.course-filter {
    height: 100vh;
    display: flex;
    flex-direction: column;
    background: #f5f5f5;
}

// 顶部筛选框
.search-bar {
    background: white;
    padding: 12px 16px;
    border-bottom: 1px solid #e8e8e8;
    flex-shrink: 0;

    .search-input-wrapper {
        position: relative;
        display: flex;
        align-items: center;
        background: #f8f9fa;
        border-radius: 20px;
        padding: 8px 16px;

        .search-icon {
            color: #999;
            margin-right: 8px;
        }

        .search-input {
            flex: 1;
            border: none;
            background: transparent;
            outline: none;
            font-size: 14px;
            color: #333;

            &::placeholder {
                color: #999;
            }
        }

        .clear-icon {
            color: #999;
            cursor: pointer;
            position: absolute;
            right: 16px;
            top: 50%;
            transform: translateY(-50%);
        }
    }
}

.filter-container {
    flex: 1;
    display: flex;
    height: 100%;
    min-height: 0; // 确保flex子元素可以收缩
}

// 左侧部门筛选
.department-filter {
    width: 150px;
    background: #f5f5f5;
    flex-shrink: 0;

    .department-list {
        padding: 10px 0;
    }

    .department-item {
        padding: 18px 14px;
        font-size: 17px;
        color: #666;
        cursor: pointer;
        transition: all 0.2s ease;
        border-right: 3px solid transparent;
        text-align: center;
        line-height: 1.3;
        position: relative;

        &.active {
            color: #1677ff;
            font-weight: 500;
        }
    }
}

// 右侧内容区域
.content-area {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: white;
    min-width: 0; // 确保flex子元素可以收缩
}

// 课程标签筛选
.tag-filter {
    padding: 14px 18px;
    background: white;
    flex-shrink: 0;

    .tag-list {
        display: flex;
        flex-wrap: nowrap;
        white-space: nowrap;
        gap: 10px;
        overflow-x: auto;
        padding-bottom: 6px;

        // 隐藏滚动条但保持滚动功能
        scrollbar-width: none;
        -ms-overflow-style: none;
        &::-webkit-scrollbar {
            display: none;
        }
    }

    .tag-item {
        padding: 8px 14px;
        font-size: 14px;
        color: #666;
        background: #f5f5f5;
        border-radius: 18px;
        cursor: pointer;
        transition: all 0.2s ease;
        white-space: nowrap;
        flex-shrink: 0;
        font-weight: 500;

        &:active {
            background: #e6f7ff;
            color: #1677ff;
        }

        &.active {
            background: #1677ff;
            color: white;
        }
    }
}

// 课程列表
.course-list {
    flex: 1;
    padding: 16px;
    padding-top: 0;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;

    scrollbar-width: none;
    -ms-overflow-style: none;
    &::-webkit-scrollbar {
        display: none;
    }

    .course-item {
        background: white;
        border-radius: 16px;
        padding: 20px;
        margin-bottom: 16px;
        cursor: pointer;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
        border: 1px solid rgba(0, 0, 0, 0.04);
        display: flex;
        align-items: flex-start;
        gap: 16px;

        &:hover {
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.08);
            border-color: rgba(22, 119, 255, 0.1);
        }

        &:last-child {
            margin-bottom: 0;
        }
    }

    .course-image {
        flex-shrink: 0;

        .image-container {
            width: 60px;
            height: 60px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;

            &.document {
                background: linear-gradient(135deg, #fff7e6 0%, #ffecc7 100%);
            }

            &.video {
                background: linear-gradient(135deg, #f6ffed 0%, #e6f7ff 100%);
            }

            .course-image-img {
                width: 100%;
                height: 100%;
                object-fit: cover;
                border-radius: 8px;
                transition: transform 0.2s ease;

                &:hover {
                    transform: scale(1.05);
                }
            }

            // 后备图标样式（图片加载失败时显示）
            .fallback-icon {
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                font-size: 24px;
                background: rgba(255, 255, 255, 0.9);
                border-radius: 50%;
                padding: 8px;

                &.pi-file-pdf {
                    color: #fa8c16;
                }

                &.pi-play-circle {
                    color: #52c41a;
                }

                &.pi-shield {
                    color: #722ed1;
                }

                &.pi-briefcase {
                    color: #1677ff;
                }

                &.pi-shopping-bag {
                    color: #eb2f96;
                }

                &.pi-sparkles {
                    color: #13c2c2;
                }
            }
        }
    }

    .course-content {
        flex: 1;
        min-width: 0;
    }

    .course-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 12px;

        .course-title {
            font-size: 16px;
            font-weight: 600;
            color: #262626;
            margin: 0;
            flex: 1;
            margin-right: 12px;
            line-height: 1.5;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .course-type-badge {
            flex-shrink: 0;
            display: flex;
            align-items: center;
            justify-content: center;

            .badge-image {
                width: auto;
                height: 20px;
                max-width: 60px;
                object-fit: contain;
                border-radius: 4px;
            }
        }
    }

    .course-info {
        .info-row {
            display: flex;
            gap: 20px;
            flex-wrap: wrap;
        }

        .info-item {
            display: flex;
            align-items: center;
            gap: 6px;
            font-size: 14px;
            color: #595959;

            .info-icon {
                font-size: 14px;
                color: #8c8c8c;
                flex-shrink: 0;
            }

            .value {
                font-weight: 500;
            }
        }
    }
}

// 空状态
.empty-state {
    text-align: center;
    padding: 80px 20px;
    color: #999;
    background: white;
    border-radius: 10px;
    margin: 20px 0;

    i {
        font-size: 48px;
        margin-bottom: 16px;
        display: block;
        color: #d9d9d9;
    }

    p {
        font-size: 15px;
        margin: 0;
    }
}

// 响应式设计
@media (max-width: 480px) {
    .department-filter {
        width: 110px;

        .department-item {
            padding: 14px 10px;
            font-size: 14px;
        }
    }

    .tag-filter {
        padding: 10px 14px;

        .tag-item {
            padding: 6px 10px;
            font-size: 12px;
        }
    }

    .course-list {
        padding: 10px;
        padding-top: 0;

        .course-item {
            padding: 12px;
            gap: 10px;
            margin-bottom: 12px;
            border-radius: 12px;
        }

        .course-image .image-container {
            width: 48px;
            height: 48px;
            border-radius: 10px;

            .course-image-img {
                border-radius: 6px;
            }

            .fallback-icon {
                font-size: 16px;
                padding: 6px;
            }
        }

        .course-header {
            margin-bottom: 8px;

            .course-title {
                font-size: 14px;
                line-height: 1.4;
                margin-right: 8px;
            }

            .course-type-badge .badge-image {
                height: 18px;
                max-width: 50px;
            }
        }

        .course-info {
            .info-row {
                gap: 12px;
            }

            .info-item {
                font-size: 12px;
                gap: 4px;

                .info-icon {
                    font-size: 12px;
                }
            }
        }
    }

    .search-bar {
        padding: 10px 12px;

        .search-input-wrapper {
            padding: 8px 16px;

            .search-icon {
                font-size: 16px;
            }

            .search-input {
                font-size: 13px;
            }

            .clear-icon {
                font-size: 16px;
            }
        }
    }
}

@media (min-width: 768px) {
    .department-filter {
        width: 170px;

        .department-item {
            padding: 20px 18px;
            font-size: 18px;
        }
    }

    .tag-filter {
        padding: 16px 22px;

        .tag-item {
            padding: 10px 16px;
            font-size: 15px;
        }
    }

    .course-list {
        padding: 24px;
        padding-top: 0;

        .course-item {
            padding: 24px;
            gap: 20px;
        }

        .course-image .image-container {
            width: 70px;
            height: 70px;

            .course-image-img {
                border-radius: 10px;
            }

            .fallback-icon {
                font-size: 24px;
                padding: 10px;
            }
        }

        .course-header .course-title {
            font-size: 18px;
        }

        .course-info {
            .info-row {
                gap: 24px;
            }

            .info-item {
                font-size: 15px;

                .info-icon {
                    font-size: 15px;
                }
            }
        }
    }
}
</style>
